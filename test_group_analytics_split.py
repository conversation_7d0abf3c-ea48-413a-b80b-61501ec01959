#!/usr/bin/env python3
"""
Тестовый скрипт для проверки разделения статистики по группам
"""
import asyncio
import sys
import os
from unittest.mock import AsyncMock, MagicMock

# Добавляем путь к корневой папке проекта
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import init_database
from common.statistics import (
    show_group_analytics, show_group_microtopics_detailed, 
    show_group_rating, get_group_stats
)
from common.analytics.keyboards import (
    get_group_analytics_kb, get_back_to_group_analytics_kb
)


async def test_group_analytics_split():
    """Тестирование разделения статистики по группам"""
    
    await init_database()
    
    print("🎯 ТЕСТ: Разделение статистики по группам")
    print("=" * 60)
    
    # Тестируем с реальной группой
    group_id = "1"  # МАТ-1 (Математика)
    
    print(f"\n📋 ТЕСТ 1: Основная функция show_group_analytics")
    print("-" * 50)
    
    # Создаем мок объекты
    callback = MagicMock()
    callback.data = f"analytics_group_{group_id}"
    callback.message.edit_text = AsyncMock()
    
    state = AsyncMock()
    state.get_data = AsyncMock(return_value={})
    
    try:
        await show_group_analytics(callback, state, "curator")
        
        print(f"✅ show_group_analytics выполнена успешно")
        print(f"   - callback.message.edit_text вызван: {callback.message.edit_text.called}")
        
        if callback.message.edit_text.called:
            call_args = callback.message.edit_text.call_args
            text = call_args[0][0]
            print(f"   - Текст содержит базовую информацию: {'Группа:' in text}")
            print(f"   - Текст содержит выбор: {'Выберите, что хотите посмотреть' in text}")
            print(f"   - Клавиатура передана: {call_args[1]['reply_markup'] is not None}")
            
    except Exception as e:
        print(f"❌ ОШИБКА: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n📋 ТЕСТ 2: Клавиатура get_group_analytics_kb")
    print("-" * 50)
    
    try:
        keyboard = get_group_analytics_kb(int(group_id))
        print(f"✅ Клавиатура создана успешно")
        print(f"   - Количество рядов кнопок: {len(keyboard.inline_keyboard)}")
        
        for i, row in enumerate(keyboard.inline_keyboard):
            for j, button in enumerate(row):
                print(f"     • {button.text} → {button.callback_data}")
                
    except Exception as e:
        print(f"❌ ОШИБКА: {e}")
    
    print(f"\n📋 ТЕСТ 3: Функция show_group_microtopics_detailed")
    print("-" * 50)
    
    callback2 = MagicMock()
    callback2.data = f"group_microtopics_detailed_{group_id}"
    callback2.message.edit_text = AsyncMock()
    
    try:
        await show_group_microtopics_detailed(callback2, state)
        
        print(f"✅ show_group_microtopics_detailed выполнена успешно")
        print(f"   - callback.message.edit_text вызван: {callback2.message.edit_text.called}")
        
        if callback2.message.edit_text.called:
            call_args = callback2.message.edit_text.call_args
            text = call_args[0][0]
            print(f"   - Текст содержит микротемы: {'понимания по микротемам' in text}")
            print(f"   - Текст НЕ содержит рейтинг: {'Рейтинг по баллам' not in text}")
            
    except Exception as e:
        print(f"❌ ОШИБКА: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n📋 ТЕСТ 4: Функция show_group_rating")
    print("-" * 50)
    
    callback3 = MagicMock()
    callback3.data = f"group_rating_{group_id}"
    callback3.message.edit_text = AsyncMock()
    
    try:
        await show_group_rating(callback3, state)
        
        print(f"✅ show_group_rating выполнена успешно")
        print(f"   - callback.message.edit_text вызван: {callback3.message.edit_text.called}")
        
        if callback3.message.edit_text.called:
            call_args = callback3.message.edit_text.call_args
            text = call_args[0][0]
            print(f"   - Текст содержит рейтинг: {'Рейтинг по баллам' in text}")
            print(f"   - Текст НЕ содержит микротемы: {'понимания по микротемам' not in text}")
            
    except Exception as e:
        print(f"❌ ОШИБКА: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n📋 ТЕСТ 5: Получение данных группы")
    print("-" * 50)
    
    try:
        group_data = await get_group_stats(group_id)
        print(f"✅ Данные группы получены:")
        print(f"   - Название: {group_data['name']}")
        print(f"   - Предмет: {group_data['subject']}")
        print(f"   - % выполнения ДЗ: {group_data['homework_completion']}%")
        print(f"   - Количество микротем: {len(group_data['topics'])}")
        print(f"   - Количество студентов в рейтинге: {len(group_data['rating'])}")
        
    except Exception as e:
        print(f"❌ ОШИБКА: {e}")
    
    print(f"\n🎉 ЗАКЛЮЧЕНИЕ:")
    print("=" * 60)
    print("✅ ИЗМЕНЕНИЯ РЕАЛИЗОВАНЫ:")
    print("   1. show_group_analytics теперь показывает базовую информацию + кнопки выбора")
    print("   2. Создана клавиатура get_group_analytics_kb с двумя кнопками")
    print("   3. Добавлена функция show_group_microtopics_detailed - только микротемы")
    print("   4. Добавлена функция show_group_rating - только рейтинг")
    print("   5. Добавлены обработчики в register_handlers.py")
    
    print(f"\n📊 РЕЗУЛЬТАТ:")
    print("   - Вместо одного длинного сообщения теперь:")
    print("     • Базовая информация о группе")
    print("     • Кнопка '📈 % понимания по микротемам'")
    print("     • Кнопка '📋 Рейтинг по баллам'")
    print("   - Каждая кнопка ведет к отдельному экрану с соответствующей информацией")


if __name__ == "__main__":
    asyncio.run(test_group_analytics_split())
