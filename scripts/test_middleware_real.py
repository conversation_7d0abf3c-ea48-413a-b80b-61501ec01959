#!/usr/bin/env python3
"""
Реальный тест производительности RoleMiddleware с подключением к БД
"""
import asyncio
import time
import sys
import os
from typing import Dict, Any

# Добавляем корневую папку в путь
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from middlewares.role_middleware import RoleMiddleware, force_update_role_cache
from utils.logging_config import setup_logging
from database import init_database
from unittest.mock import Mock


class MockEvent:
    """Мок объект для имитации Message/CallbackQuery"""
    def __init__(self, user_id: int):
        self.from_user = Mock()
        self.from_user.id = user_id


async def test_real_middleware():
    """Тестирование middleware с реальной БД"""
    print("🧪 Реальный тест производительности RoleMiddleware")
    print("=" * 55)
    
    # Настраиваем логирование
    setup_logging()
    
    # Инициализируем БД
    try:
        await init_database()
        print("✅ База данных подключена")
    except Exception as e:
        print(f"❌ Ошибка подключения к БД: {e}")
        print("🔄 Продолжаем тест без БД (fallback режим)")
    
    # Создаем middleware
    middleware = RoleMiddleware()
    
    # Мок handler
    async def mock_handler(event, data):
        return f"OK"
    
    # Тестовые пользователи (включая реальных админов)
    test_users = [
        955518340,  # Реальный админ
        5205775566, # Реальный админ
        123456789,  # Тестовый пользователь
        987654321,  # Тестовый пользователь
        111111111,  # Тестовый пользователь
    ]
    
    print(f"👥 Тестируем {len(test_users)} пользователей")
    
    # Принудительно обновляем кэш
    print("\n🔄 Принудительное обновление кэша...")
    start_time = time.time()
    await force_update_role_cache()
    cache_update_time = time.time() - start_time
    print(f"⏱️  Время обновления кэша: {cache_update_time:.3f}с")
    
    # Тест 1: Первые запросы после обновления кэша
    print("\n🚀 Тест 1: Запросы с актуальным кэшем")
    start_time = time.time()
    
    results = []
    for user_id in test_users:
        event = MockEvent(user_id)
        data = {}
        result = await middleware(mock_handler, event, data)
        role = data.get('user_role', 'unknown')
        results.append((user_id, role))
        print(f"  User {user_id}: role={role}")
    
    warm_cache_time = time.time() - start_time
    print(f"⏱️  Время с актуальным кэшем: {warm_cache_time:.3f}с")
    
    # Тест 2: Массовые запросы
    print("\n⚡ Тест 2: Массовые запросы (200 запросов)")
    start_time = time.time()
    
    tasks = []
    for i in range(200):
        user_id = test_users[i % len(test_users)]
        event = MockEvent(user_id)
        data = {}
        task = middleware(mock_handler, event, data)
        tasks.append(task)
    
    await asyncio.gather(*tasks)
    mass_time = time.time() - start_time
    print(f"⏱️  Время 200 запросов: {mass_time:.3f}с")
    print(f"📈 Среднее время на запрос: {mass_time/200*1000:.2f}мс")
    
    # Тест 3: Конкурентные запросы
    print("\n🔀 Тест 3: Конкурентные запросы (50 одновременных)")
    start_time = time.time()
    
    async def concurrent_request(user_id):
        event = MockEvent(user_id)
        data = {}
        return await middleware(mock_handler, event, data)
    
    # Создаем 50 одновременных задач
    concurrent_tasks = [
        concurrent_request(test_users[i % len(test_users)]) 
        for i in range(50)
    ]
    
    await asyncio.gather(*concurrent_tasks)
    concurrent_time = time.time() - start_time
    print(f"⏱️  Время 50 конкурентных запросов: {concurrent_time:.3f}с")
    print(f"📈 Среднее время на запрос: {concurrent_time/50*1000:.2f}мс")
    
    # Проверка состояния кэша
    from middlewares.role_middleware import _global_role_cache, _global_cache_updated, _last_cache_update
    print(f"\n🗂️  Состояние кэша:")
    print(f"  Обновлен: {_global_cache_updated}")
    print(f"  Последнее обновление: {time.time() - _last_cache_update:.1f}с назад")
    
    total_users = sum(len(users) for users in _global_role_cache.values())
    print(f"  Всего пользователей в кэше: {total_users}")
    
    for role, users in _global_role_cache.items():
        if users:
            print(f"    {role}: {len(users)} пользователей")
    
    # Итоговые результаты
    print(f"\n📊 Итоговые результаты производительности:")
    print(f"  🔄 Обновление кэша: {cache_update_time:.3f}с")
    print(f"  🚀 Актуальный кэш: {warm_cache_time:.3f}с")
    print(f"  ⚡ Массовые запросы: {mass_time:.3f}с")
    print(f"  🔀 Конкурентные запросы: {concurrent_time:.3f}с")
    
    # Анализ производительности
    if mass_time/200 < 0.01:  # < 10ms на запрос
        print("✅ Производительность отличная!")
    elif mass_time/200 < 0.05:  # < 50ms на запрос
        print("✅ Производительность хорошая")
    else:
        print("⚠️  Производительность может быть улучшена")
    
    # Проверка корректности ролей
    print(f"\n🔍 Проверка корректности ролей:")
    for user_id, role in results:
        if user_id in [955518340, 5205775566]:
            expected = "admin"
        else:
            expected = "student"
        
        status = "✅" if role == expected else "❌"
        print(f"  {status} User {user_id}: {role} (ожидалось: {expected})")


if __name__ == "__main__":
    asyncio.run(test_real_middleware())
