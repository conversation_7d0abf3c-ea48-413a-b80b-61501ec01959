#!/usr/bin/env python3
"""
Скрипт для тестирования производительности RoleMiddleware
"""
import asyncio
import time
import sys
import os
from typing import Dict, Any

# Добавляем корневую папку в путь
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from middlewares.role_middleware import RoleMiddleware
from aiogram.types import Message, User, Chat
from unittest.mock import Mock, AsyncMock


class MockEvent:
    """Мок объект для имитации Message/CallbackQuery"""
    def __init__(self, user_id: int):
        self.from_user = Mock()
        self.from_user.id = user_id


async def test_middleware_performance():
    """Тестирование производительности middleware"""
    print("🧪 Тестирование производительности RoleMiddleware")
    print("=" * 50)
    
    # Создаем экземпляр middleware
    middleware = RoleMiddleware()
    
    # Мок handler
    async def mock_handler(event, data):
        return f"Handled for user {event.from_user.id} with role {data.get('user_role')}"
    
    # Тестовые пользователи
    test_users = [955518340, 5205775566, 123456789, 987654321, 111111111]
    
    print(f"📊 Тестируем {len(test_users)} пользователей")
    
    # Тест 1: Первый запуск (холодный кэш)
    print("\n🔥 Тест 1: Холодный кэш (первый запуск)")
    start_time = time.time()
    
    for user_id in test_users:
        event = MockEvent(user_id)
        data = {}
        result = await middleware(mock_handler, event, data)
        print(f"  User {user_id}: role={data.get('user_role')} | result={result}")
    
    cold_time = time.time() - start_time
    print(f"⏱️  Время холодного кэша: {cold_time:.3f}с")
    
    # Тест 2: Повторные запросы (горячий кэш)
    print("\n🚀 Тест 2: Горячий кэш (повторные запросы)")
    start_time = time.time()
    
    for user_id in test_users:
        event = MockEvent(user_id)
        data = {}
        result = await middleware(mock_handler, event, data)
    
    hot_time = time.time() - start_time
    print(f"⏱️  Время горячего кэша: {hot_time:.3f}с")
    
    # Тест 3: Массовые запросы (имитация нагрузки)
    print("\n⚡ Тест 3: Массовые запросы (100 запросов)")
    start_time = time.time()
    
    tasks = []
    for i in range(100):
        user_id = test_users[i % len(test_users)]
        event = MockEvent(user_id)
        data = {}
        task = middleware(mock_handler, event, data)
        tasks.append(task)
    
    results = await asyncio.gather(*tasks)
    mass_time = time.time() - start_time
    print(f"⏱️  Время 100 запросов: {mass_time:.3f}с")
    print(f"📈 Среднее время на запрос: {mass_time/100*1000:.1f}мс")
    
    # Результаты
    print("\n📋 Итоговые результаты:")
    print(f"  🔥 Холодный кэш: {cold_time:.3f}с")
    print(f"  🚀 Горячий кэш: {hot_time:.3f}с")
    print(f"  ⚡ Массовые запросы: {mass_time:.3f}с")
    if hot_time > 0:
        print(f"  📊 Ускорение: {cold_time/hot_time:.1f}x")
    else:
        print(f"  📊 Горячий кэш настолько быстр, что время < 0.001с")
    
    # Проверка состояния кэша
    from middlewares.role_middleware import _global_role_cache, _global_cache_updated
    print(f"\n🗂️  Состояние кэша:")
    print(f"  Обновлен: {_global_cache_updated}")
    print(f"  Размер кэша: {sum(len(users) for users in _global_role_cache.values())} пользователей")
    for role, users in _global_role_cache.items():
        if users:
            print(f"    {role}: {len(users)} пользователей")


async def test_cache_ttl():
    """Тестирование TTL кэша"""
    print("\n🕐 Тестирование TTL кэша")
    print("=" * 30)
    
    from middlewares.role_middleware import _last_cache_update, CACHE_TTL
    
    middleware = RoleMiddleware()
    
    # Мок handler
    async def mock_handler(event, data):
        return "OK"
    
    event = MockEvent(955518340)
    data = {}
    
    # Первый запрос
    await middleware(mock_handler, event, data)
    first_update = _last_cache_update
    print(f"Первое обновление кэша: {first_update}")
    
    # Ждем немного
    await asyncio.sleep(1)
    
    # Второй запрос (кэш должен быть актуальным)
    await middleware(mock_handler, event, data)
    second_update = _last_cache_update
    print(f"Второе обновление кэша: {second_update}")
    
    if first_update == second_update:
        print("✅ TTL работает корректно - кэш не обновлялся")
    else:
        print("❌ TTL не работает - кэш обновился повторно")
    
    print(f"TTL кэша: {CACHE_TTL} секунд")


if __name__ == "__main__":
    asyncio.run(test_middleware_performance())
    asyncio.run(test_cache_ttl())
